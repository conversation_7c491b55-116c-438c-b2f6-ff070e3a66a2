import React, { useState, useEffect, useRef, useCallback } from 'react'
import { render, GsapTimeline, useCaspar, useCasparData } from '@nxtedition/graphics-kit'
import gsap from 'gsap'
import './style.css'

const VTClock = () => {
  const { isPlaying } = useCaspar()
  const {
    duration = 30,
    title = "ITV NEWS",
    programmeName = "NAT BONGS",
    tapeId = "NET31TU25-NN22",
    date = "TUESDAY 29 JULY",
    presenter = "TOM BRADBY",
    takeNumber = "TAKE 2",
    autoStart = false
  } = useCasparData()

  const [timeRemaining, setTimeRemaining] = useState(duration)
  const [isCountdownActive, setIsCountdownActive] = useState(false)

  const handRef = useRef(null)        // <g> element inside svg
  const containerRef = useRef(null)
  const intervalRef = useRef(null)
  const startTimeRef = useRef(null)

  // Map timeRemaining -> rotation degrees.
  // We want: 0s => 0° (12 o'clock), duration => 180° (6 o'clock)
  const getHandAngle = useCallback((time) => {
    const clamped = Math.max(0, Math.min(time, duration))
    const progress = clamped / duration
    return progress * 180 // 0 -> 0deg, duration -> 180deg
  }, [duration])

  const startCountdown = useCallback(() => {
    if (isCountdownActive) return

    setIsCountdownActive(true)
    setTimeRemaining(duration)
    startTimeRef.current = Date.now()

    // kill any existing tween
    gsap.killTweensOf(handRef.current)

    // set start transform explicitly (rotate(startAngle cx cy))
    if (handRef.current) {
      const startAngle = getHandAngle(duration)
      handRef.current.setAttribute('transform', `rotate(${startAngle} 250 250)`)
      // Tween to 0deg over `duration` seconds for smooth travel
      gsap.to(handRef.current, {
        attr: { transform: `rotate(0 250 250)` },
        duration,
        ease: 'linear'
      })
    }

    // update numeric time via interval (keeps your UI logic intact)
    intervalRef.current = setInterval(() => {
      const elapsed = (Date.now() - startTimeRef.current) / 1000
      const remaining = Math.max(0, duration - elapsed)
      setTimeRemaining(remaining)

      if (remaining <= 0) {
        setIsCountdownActive(false)
        clearInterval(intervalRef.current)
        // ensure final position is exactly 0
        if (handRef.current) handRef.current.setAttribute('transform', `rotate(0 250 250)`)
        setTimeRemaining(0)
      }
    }, 100)
  }, [duration, isCountdownActive, getHandAngle])

  const stopCountdown = useCallback(() => {
    setIsCountdownActive(false)
    if (intervalRef.current) clearInterval(intervalRef.current)
    gsap.killTweensOf(handRef.current)
  }, [])

  // init hand to duration position
  useEffect(() => {
    if (handRef.current) {
      const startAngle = getHandAngle(duration)
      handRef.current.setAttribute('transform', `rotate(${startAngle} 250 250)`)
    }
  }, [duration, getHandAngle])

  useEffect(() => {
    if (!isCountdownActive) {
      setTimeRemaining(duration)
      if (handRef.current) {
        const startAngle = getHandAngle(duration)
        handRef.current.setAttribute('transform', `rotate(${startAngle} 250 250)`)
      }
    }
  }, [duration, isCountdownActive, getHandAngle])

  useEffect(() => {
    if (autoStart && isPlaying) {
      const t = setTimeout(() => startCountdown(), 500)
      return () => clearTimeout(t)
    }
  }, [autoStart, isPlaying, startCountdown])

  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
      gsap.killTweensOf(handRef.current)
    }
  }, [])

  const formatTime = (seconds) => {
    const displaySeconds = Math.max(0, Math.ceil(seconds))
    return displaySeconds.toString()
  }

  // Generate ticks: 0 at top (12 o'clock), duration at bottom (6 o'clock)
  const generateTickMarks = () => {
    const ticks = []
    const numbers = []
    const centerX = 250
    const centerY = 250
    const radius = 200

    for (let i = 0; i <= duration; i += 5) {
      // Map 0..duration -> -90..+90 degrees (top -> bottom)
      const angle = (i / duration) * 180 - 90
      const radians = (angle * Math.PI) / 180

      const x1 = centerX + Math.cos(radians) * (radius - 15)
      const y1 = centerY + Math.sin(radians) * (radius - 15)
      const x2 = centerX + Math.cos(radians) * radius
      const y2 = centerY + Math.sin(radians) * radius

      ticks.push(
        <line
          key={`tick-${i}`}
          className="vtClock-tick"
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
        />
      )

      const numX = centerX + Math.cos(radians) * (radius + 25)
      const numY = centerY + Math.sin(radians) * (radius + 25)

      numbers.push(
        <text
          key={`num-${i}`}
          className="vtClock-tickNumber"
          x={numX}
          y={numY}
          textAnchor="middle"
          dominantBaseline="middle"
        >
          {i}
        </text>
      )
    }

    return { ticks, numbers }
  }

  const { ticks, numbers } = generateTickMarks()

  return (
    <GsapTimeline
      hide={!isPlaying}
      onPlay={timeline => {
        timeline.from(containerRef.current, { opacity: 0, duration: 0.5, ease: "power2.out" })
      }}
      onStop={timeline => {
        timeline.to(containerRef.current, { opacity: 0, duration: 0.5, ease: "power2.in" })
        stopCountdown()
      }}
    >
      <div ref={containerRef} className="vtClock-container">
        <div className="vtClock-left">
          <div className="vtClock-clockContainer">
            <div className="vtClock-clockFace">
              <svg className="vtClock-arc" viewBox="0 0 500 500" preserveAspectRatio="xMidYMid meet">
                <path className="vtClock-arcPath" d="M 250 50 A 200 200 0 0 1 250 450" />
              </svg>

              <svg className="vtClock-tickMarks" viewBox="0 0 500 500" preserveAspectRatio="xMidYMid meet">
                {ticks}
                {numbers}
              </svg>

              {/* Hand SVG: we keep the line pointing up (250,250)->(250,70) and rotate the <g> around 250,250 */}
              <svg className="vtClock-handSvg" viewBox="0 0 500 500" preserveAspectRatio="xMidYMid meet">
                <g ref={handRef} transform={`rotate(${getHandAngle(timeRemaining)} 250 250)`}>
                  <line className="vtClock-handLine" x1="250" y1="250" x2="250" y2="70" />
                </g>
              </svg>
            </div>

            <div className="vtClock-centerCircle">
              <div className="vtClock-timeDisplay">
                {formatTime(timeRemaining)}
              </div>
            </div>
          </div>
        </div>

        <div className="vtClock-right">
          <div className="vtClock-title">{title}</div>

          <div className="vtClock-textField">
            <div className="vtClock-fieldValue bold">{programmeName}</div>
          </div>

          <div className="vtClock-textField"><div className="vtClock-fieldValue">{tapeId}</div></div>
          <div className="vtClock-textField"><div className="vtClock-fieldValue">{date}</div></div>
          <div className="vtClock-textField"><div className="vtClock-fieldValue">{presenter}</div></div>
          <div className="vtClock-textField"><div className="vtClock-fieldValue bold">{takeNumber}</div></div>
        </div>
      </div>
    </GsapTimeline>
  )
}

render(VTClock)
