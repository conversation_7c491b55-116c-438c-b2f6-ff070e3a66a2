@import url("https://fonts.googleapis.com/css2?family=Arial:wght@400;700;900&display=swap");

.vtClock-container {
  position: fixed;
  /* top: 0;
  left: 0; */
  width: 100vw;
  height: 100vh;
  background: #000000;
  display: flex;
  font-family: Arial, sans-serif;
  color: white;
  overflow: hidden;
}

/* Left Side - Traditional Countdown Clock */
.vtClock-left {
  width: 50%;
  height: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: #000000;
}

/* Traditional Semicircular Clock */
.vtClock-clockContainer {
  position: relative;
  width: 700px;
  height: 560px;
}

.vtClock-clockFace {
  position: relative;
  width: 700px;
  height: 560px;
  overflow: visible;
}

/* Semicircular Arc */
.vtClock-arc {
  position: absolute;
  top: 0;
  left: 0;
  width: 700px;
  height: 700px;
}

.vtClock-arcPath {
  fill: none;
  stroke: white;
  stroke-width: 6;
}

.vtClock-handSvg {
  position: absolute;
  top: 0;
  left: 0;
  width: 700px;
  height: 700px;
  overflow: visible; /* important so rotation isn’t clipped */
  pointer-events: none;
}

.vtClock-handLine {
  stroke: white;
  stroke-width: 8px; /* thicker for better visibility */
  fill: none;
  stroke-linecap: round;
}

/* Center Circle with Time Display */
.vtClock-centerCircle {
  position: absolute;
  top: 63%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  background: black;
  border: 6px solid white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.vtClock-timeDisplay {
  font-size: 72px;
  font-weight: 900;
  color: white;
  font-family: Arial, sans-serif;
  text-align: center;
}

/* Tick Marks and Numbers */
.vtClock-tickMarks {
  position: absolute;
  top: 0;
  left: 0;
  width: 700px;
  height: 700px;
}

.vtClock-tick {
  stroke: white;
  stroke-width: 3;
  fill: none;
}

.vtClock-tickNumber {
  fill: white;
  font-family: Arial, sans-serif;
  font-size: 28px;
  font-weight: bold;
}

/* Right Side - Traditional Broadcast Text */
.vtClock-right {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80px;
  background: #000000;
}

.vtClock-textField {
  margin-bottom: 25px;
  text-align: center;
  width: 100%;
}

.vtClock-title {
  font-size: 48px;
  font-weight: normal;
  color: white;
  font-family: Arial, sans-serif;
  margin-bottom: 40px;
  letter-spacing: 2px;
  text-align: center;
}

.vtClock-fieldValue {
  font-size: 34px;
  font-weight: normal;
  color: white;
  font-family: Arial, sans-serif;
  line-height: 1.4;
  margin: 0;
}

.vtClock-fieldValue.bold {
  font-weight: bold;
}

.vtClock-fieldValue.large {
  font-size: 52px;
}

.vtClock-fieldValue.small {
  font-size: 28px;
}

.vtClock-fieldValue.large {
  font-size: 52px;
}

.vtClock-fieldValue.small {
  font-size: 28px;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .vtClock-clockContainer {
    width: 350px;
    height: 350px;
  }

  .vtClock-timeDisplay {
    font-size: 60px;
  }

  .vtClock-progressRing {
    width: 330px;
    height: 330px;
  }
}

@media (max-width: 1200px) {
  .vtClock-left,
  .vtClock-right {
    width: 50%;
  }

  .vtClock-right {
    padding: 40px;
  }

  .vtClock-fieldValue {
    font-size: 28px;
  }

  .vtClock-fieldValue.large {
    font-size: 36px;
  }
}

/* Responsive adjustments for broadcast */
@media (max-width: 1600px) {
  .vtClock-clockContainer {
    width: 400px;
    height: 240px;
  }

  .vtClock-clockFace {
    width: 400px;
    height: 200px;
  }

  .vtClock-arc,
  .vtClock-handSvg,
  .vtClock-tickMarks {
    width: 400px;
    height: 200px;
  }
}
